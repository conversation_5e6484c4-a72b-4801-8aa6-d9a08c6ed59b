Index: app/Model/Image/Setup/ImageSizeConfiguration.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/Model/Image/Setup/ImageSizeConfiguration.php b/app/Model/Image/Setup/ImageSizeConfiguration.php
--- a/app/Model/Image/Setup/ImageSizeConfiguration.php	(revision 4061fca1eda372a75b061972d22e8e55cb0b37ad)
+++ b/app/Model/Image/Setup/ImageSizeConfiguration.php	(revision fa437f3bd86c87c1bb86a472e9b50fd656888030)
@@ -2,18 +2,18 @@
 
 namespace App\Model\Image\Setup;
 
-class ImageSizeConfiguration
+readonly class ImageSizeConfiguration
 {
 
 	public function __construct(
-		public readonly int $width,
-		public readonly int $height,
-		public readonly bool $keepRatio,
-		public readonly bool $fill,
-		public readonly bool $square,
-		public readonly int $quality,
-		public readonly ?string $watermark,
-		public readonly ?string $type = null,
+		public int $width,
+		public int $height,
+		public bool $keepRatio,
+		public bool $fill,
+		public bool $square,
+		public int $quality,
+		public ?string $watermark,
+		public ?string $type = null,
 	)
 	{
 	}
@@ -23,9 +23,9 @@
 	{
 		$width = $setup['width'];
 		$height = $setup['height'];
-		$keepRatio = isset($setup['keepRatio']) && (bool) $setup['keepRatio'];
-		$fill = isset($setup['fill']) && (bool) $setup['fill'];
-		$square = isset($setup['square']) && (bool) $setup['square'];
+		$keepRatio = isset($setup['keepRatio']) && $setup['keepRatio'];
+		$fill = isset($setup['fill']) && $setup['fill'];
+		$square = isset($setup['square']) && $setup['square'];
 		$watermark = (isset($setup['watermark'])) ? (string) $setup['watermark'] : '';
 		$quality = (isset($setup['quality'])) ? (int) $setup['quality'] : 100;
 
Index: app/Model/Image/Setup/ImageSizes.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/Model/Image/Setup/ImageSizes.php b/app/Model/Image/Setup/ImageSizes.php
--- a/app/Model/Image/Setup/ImageSizes.php	(revision 4061fca1eda372a75b061972d22e8e55cb0b37ad)
+++ b/app/Model/Image/Setup/ImageSizes.php	(revision fa437f3bd86c87c1bb86a472e9b50fd656888030)
@@ -2,10 +2,10 @@
 
 namespace App\Model\Image\Setup;
 
-class ImageSizes
+readonly class ImageSizes
 {
 	public function __construct(
-		private readonly array $setup,
+		private array $setup,
 	) {}
 
 
Index: app/Model/Image/ImageHelper.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/Model/Image/ImageHelper.php b/app/Model/Image/ImageHelper.php
--- a/app/Model/Image/ImageHelper.php	(revision 4061fca1eda372a75b061972d22e8e55cb0b37ad)
+++ b/app/Model/Image/ImageHelper.php	(revision fa437f3bd86c87c1bb86a472e9b50fd656888030)
@@ -2,6 +2,11 @@
 
 namespace App\Model\Image;
 
+use App\Exceptions\LogicException;
+use App\Model\ConfigService;
+use Nette\Utils\ImageException;
+use Nette\Utils\UnknownImageFileException;
+
 class ImageHelper
 {
 
@@ -19,4 +24,27 @@
 
 		return $extensions[$ext] ?? null;
 	}
+
+	/**
+	 * @throws ImageException
+	 * @throws UnknownImageFileException
+	 * @throws LogicException
+	 */
+	public static function resolveImageSize(ConfigService $configService, string $originalPath): array
+	{
+		if ($configService->get('imageOriginal', 'resize')) {
+			// resize Orig Image - to save space on disk
+			$NewImageWidth      = $configService->get('imageOriginal', 'width'); //New Width of Image
+			$NewImageHeight     = $configService->get('imageOriginal', 'height'); // New Height of Image
+			$imagePath = $originalPath;
+			$destPath = $originalPath;
+			if (file_exists($imagePath) && @getimagesize($imagePath) !== false) {
+				//Continue only if 2 given parameters are true
+				//Image looks valid, resize.
+				return Resizer::resizeImage($imagePath, $destPath, $NewImageWidth, $NewImageHeight, 95);
+			}
+		}
+
+		throw new LogicException("Unable to calculate image size");
+	}
 }
Index: app/Model/Image/ImageObjectFactory.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/Model/Image/ImageObjectFactory.php b/app/Model/Image/ImageObjectFactory.php
--- a/app/Model/Image/ImageObjectFactory.php	(revision 4061fca1eda372a75b061972d22e8e55cb0b37ad)
+++ b/app/Model/Image/ImageObjectFactory.php	(revision fa437f3bd86c87c1bb86a472e9b50fd656888030)
@@ -5,50 +5,58 @@
 use App\Model\Image\Setup\ImageSizeConfiguration;
 use App\Model\Image\Setup\ImageSizes;
 use App\Model\Image\Storage\BasicStorage;
+use Nette\Utils\Image;
 use Nette\Utils\UnknownImageFileException;
 
-final class ImageObjectFactory
+final readonly class ImageObjectFactory
 {
 
 	public function __construct(
-		private readonly BasicStorage $storage,
-		private readonly Resizer $resizer,
-		private readonly ImageSizes $setup,
-		private readonly string $wwwDir,
-		private readonly string $noImageFile = '',
+		private BasicStorage $storage,
+		private Resizer $resizer,
+		private ImageSizes $setup,
+		private string $wwwDir,
+		private string $noImageFile = '',
 	)
 	{
 	}
 
-
-
-	public function getByName(?string $fileNameWithExtension, string $sizeName, ?int $timestamp = null): ImageObject
+	public function getByName(?string $fileNameWithExtension, string $sizeName, ?int $timestamp = null, ?int $width = null, ?int $height = null): ImageObject
 	{
-        // bd($timestamp);
 		$fileInfo = pathinfo($fileNameWithExtension);
 
-		$extension = '';
-		if (isset($fileInfo['extension'])) {
-			$extension = $fileInfo['extension'];
-		}
+		$extension = $fileInfo['extension'] ?? '';
 
 		$fileName = $fileInfo['filename'];
 
-        $extension = strtolower($extension);
-        $setup = $this->setup->getSetupByType($sizeName);
+		$extension = strtolower($extension);
+		$setup = $this->setup->getSetupByType($sizeName);
 
-        $imageSrc = $this->storage->getImageSrc($setup, $fileName, $extension, $timestamp);
-        $imagePath = $this->storage->getPathToImage($setup, $fileName, $extension);
+		if(!$setup->keepRatio && !$setup->fill && $width && $height){
+			[$width, $height] = Image::calculateSize($width, $height, $setup->width, $setup->height);
+		}else{
+			$width = $setup->width;
+			$height = $setup->height;
+		}
+
+		$imageSrc = $this->storage->getImageSrc($setup, $fileName, $extension, $timestamp);
+		$imagePath = $this->storage->getPathToImage($setup, $fileName, $extension);
 
-        return new ImageObject(ext: $extension, src: $imageSrc, path: $imagePath, width: $setup->width, height: $setup->height);
+		return new ImageObject(ext: $extension, src: $imageSrc, path: $imagePath, width: $width, height: $height);
 	}
 
+	/**
+	 * @throws UnknownImageFileException
+	 */
 	public function get(string $fileName, string $extension, string $sizeName, ?string $forcedExtension): ImageObject
 	{
 		$setup = $this->setup->getSetupByType($sizeName);
 		return $this->getFromStorage($setup, $fileName, $extension, $forcedExtension);
 	}
 
+	/**
+	 * @throws UnknownImageFileException
+	 */
 	private function getFromStorage(Setup\ImageSizeConfiguration $setup, string $fileName, string $extension, ?string $forceExtension = null): ImageObject
 	{
 		if ($this->storage->exist($setup, $fileName, $forceExtension ?? $extension)) {
@@ -56,24 +64,22 @@
 			$imagePath = $this->storage->getPathToImage($setup, $fileName, $forceExtension ?? $extension);
 
 			return new ImageObject(ext: $forceExtension ?? $extension, src: $imageSrc, path: $imagePath, width: $setup->width, height: $setup->height);
+		}
 
-		} else {
-			if ($this->storage->existOriginal($fileName, $extension)) {
-				$this->resizer->resample($fileName, $extension, $setup, $forceExtension);
-				$imageSrc = $this->storage->getImageSrc($setup, $fileName, $extension);
-				$imagePath = $this->storage->getPathToImage($setup, $fileName, $forceExtension ?? $extension);
+		if ($this->storage->existOriginal($fileName, $extension)) {
+			$this->resizer->resample($fileName, $extension, $setup, $forceExtension);
+			$imageSrc = $this->storage->getImageSrc($setup, $fileName, $extension);
+			$imagePath = $this->storage->getPathToImage($setup, $fileName, $forceExtension ?? $extension);
 
-				return new ImageObject(ext: $forceExtension ?? $extension, src: $imageSrc, path: $imagePath, width: $setup->width, height: $setup->height);
-			}
+			return new ImageObject(ext: $forceExtension ?? $extension, src: $imageSrc, path: $imagePath, width: $setup->width, height: $setup->height);
 		}
 
 		if ($this->noImageFile === '') {
 			throw new MissingImageException(sprintf('Image %s.%s not found.', $fileName, $extension));
-		} else {
-			throw new UnknownImageFileException(sprintf('Image %s.%s not found.', $fileName, $extension));
-		}
+		}
+
+		throw new UnknownImageFileException(sprintf('Image %s.%s not found.', $fileName, $extension));
 	}
-
 
 	public function getNoImage(string $sizeName): ImageObject
 	{
Index: app/Model/Image/Resizer.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/Model/Image/Resizer.php b/app/Model/Image/Resizer.php
--- a/app/Model/Image/Resizer.php	(revision 4061fca1eda372a75b061972d22e8e55cb0b37ad)
+++ b/app/Model/Image/Resizer.php	(revision fa437f3bd86c87c1bb86a472e9b50fd656888030)
@@ -9,16 +9,16 @@
 use Nette\Utils\UnknownImageFileException;
 use function assert;
 
-class Resizer
+readonly class Resizer
 {
 
 
 
-	private readonly array $envelopeColors;
+	private array $envelopeColors;
 
 	public function __construct(
-		private readonly string $wwwDir,
-		private readonly BasicStorage $storage
+		private string $wwwDir,
+		private BasicStorage $storage
 	)
 	{
 		$this->envelopeColors = [
@@ -52,11 +52,8 @@
 			} else {
 				$image = $image->resize($setup->width, $setup->height);
 			}
-		} else {
-			// nedochazi k resizu, rozmery obrazku jsou mensi nez pozadovane, normalizace barev pro png
-			if ($extension === 'png') {
-				$image->paletteToTrueColor();
-			}
+		} else if ($extension === 'png') {
+			$image->paletteToTrueColor();
 		}
 
 		if ($setup->square) {
@@ -74,7 +71,6 @@
 		return $image;
 	}
 
-
 	public function crop(Image $image, string $left, string $top, int $width, int $height): Image
 	{
 		[$r['x'], $r['y'], $r['width'], $r['height']]
@@ -94,4 +90,32 @@
 		return new Image($newImage);
 	}
 
+	/**
+	 * @throws ImageException
+	 * @throws UnknownImageFileException
+	 */
+	public static function resizeImage(string $SrcImage, string $DestImage, int $MaxWidth, int $MaxHeight, int $Quality): array
+	{
+		$imageSize = getimagesize($SrcImage);
+		\assert($imageSize !== false);
+
+		[$iWidth, $iHeight] = $imageSize;
+
+		if ($iWidth <= $MaxWidth && $iHeight <= $MaxHeight) {
+			return [$iWidth, $iHeight];
+		}
+
+		$ImageScale  = min($MaxWidth / $iWidth, $MaxHeight / $iHeight);
+		$NewWidth    = ceil($ImageScale * $iWidth);
+		$NewHeight   = ceil($ImageScale * $iHeight);
+
+		$image = Image::fromFile($SrcImage);
+		$NewWidth = (int) $NewWidth;
+		$NewHeight = (int) $NewHeight;
+		$image->resize($NewWidth, $NewHeight);
+		$image->save($DestImage, $Quality);
+
+		return [$NewWidth, $NewHeight];
+	}
+
 }
Index: app/Model/Orm/LibraryImage/LibraryImage.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/Model/Orm/LibraryImage/LibraryImage.php b/app/Model/Orm/LibraryImage/LibraryImage.php
--- a/app/Model/Orm/LibraryImage/LibraryImage.php	(revision 4061fca1eda372a75b061972d22e8e55cb0b37ad)
+++ b/app/Model/Orm/LibraryImage/LibraryImage.php	(revision fa437f3bd86c87c1bb86a472e9b50fd656888030)
@@ -23,6 +23,8 @@
  * @property string|null $sourceImage
  * @property string|null $md5
  * @property ArrayHash $alts {container JsonContainer}
+ * @property int $width {default 0}
+ * @property int $height {default 0}
  *
  * @property LibraryTree|null $library {m:1 LibraryTree::$images}
  * @property ProductImage[]|ManyHasOne $productImages  {1:m ProductImage::$libraryImage}
@@ -31,7 +33,6 @@
  * @property-read  string|null $url {virtual}
  * @property-read  string|null $ext {virtual}
  * @property-read  string|null $fileNameWithoutExtension {virtual}
-
  * @property-read  bool $isSvg {virtual}
  */
 class LibraryImage extends ImageEntity
@@ -39,6 +40,14 @@
 
 	use HasCache;
 	use HasImageResizer;
+	private LibraryImageModel $libraryImageModel;
+
+	public function injectService(
+		LibraryImageModel $libraryImageModel,
+	): void
+	{
+		$this->libraryImageModel = $libraryImageModel;
+	}
 
 	protected function getterUrl(): string
 	{
@@ -68,15 +77,17 @@
 
 	public function getSize(string $sizeName): ImageObject
 	{
-		return $this->imageObjectFactory->getByName($this->filename, $sizeName, $this->getTimestamp());
+		if($this->width === 0 || $this->height === 0){
+			[$width, $height] = $this->libraryImageModel->recalculateSizes($this);
+			return $this->imageObjectFactory->getByName($this->filename, $sizeName, $this->getTimestamp(), $width, $height);
+		}
+
+		return $this->imageObjectFactory->getByName($this->filename, $sizeName, $this->getTimestamp(), $this->width, $this->height);
 	}
 
 	public function getAlt(Mutation $mutation): string
 	{
-		if (isset($this->alts->{$mutation->id})) {
-			return $this->alts->{$mutation->id};
-		}
-		return $this->name;
+		return $this->alts->{$mutation->id} ?? $this->name;
 	}
 
 
Index: app/Model/Orm/LibraryImage/LibraryImageModel.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/Model/Orm/LibraryImage/LibraryImageModel.php b/app/Model/Orm/LibraryImage/LibraryImageModel.php
--- a/app/Model/Orm/LibraryImage/LibraryImageModel.php	(revision 4061fca1eda372a75b061972d22e8e55cb0b37ad)
+++ b/app/Model/Orm/LibraryImage/LibraryImageModel.php	(revision fa437f3bd86c87c1bb86a472e9b50fd656888030)
@@ -2,23 +2,28 @@
 
 namespace App\Model\Orm\LibraryImage;
 
+use App\Exceptions\LogicException;
 use App\Model\ConfigService;
 use App\Model\ElasticSearch\All\Repository;
+use App\Model\Image\ImageHelper;
 use App\Model\Image\Storage\BasicStorage;
 use App\Model\Orm\Mutation\Mutation;
 use App\Model\Orm\Orm;
+use App\Model\Utils\DirectoryUtils;
+use App\Model\Utils\FilenameUtils;
 use Nette\Http\FileUpload;
-use Nette\Utils\Image;
+use Nette\Utils\ImageException;
 use Nette\Utils\Strings;
+use Nette\Utils\UnknownImageFileException;
+use Tracy\Debugger;
 
-final class LibraryImageModel
+final readonly class LibraryImageModel
 {
-
 	public function __construct(
-		private readonly Orm           $orm,
-		private readonly ConfigService $configService,
-		private readonly BasicStorage  $imageStorage,
-		private readonly Repository  $esAllRepository,
+		private Orm           $orm,
+		private ConfigService $configService,
+		private BasicStorage  $imageStorage,
+		private Repository  $esAllRepository,
 	)
 	{
 	}
@@ -31,7 +36,6 @@
 		return $image;
 	}
 
-
 	public function addFromTmpFile(string $file, ?int $cat, ?string $name = null, bool $isCopy = false, ?string $sourceImage = null, ?string $md5 = null): LibraryImage
 	{
 		$fileName = $name ?? basename($file);
@@ -43,36 +47,25 @@
 
 		$this->orm->libraryImage->persist($newImage);
 
-		$filename = $newImage->id . '-' . $this->sanitize($fileName);
+		$filename = $newImage->id . '-' . FilenameUtils::sanitize($fileName);
 		$pathInfo = pathinfo($filename);
 		$filenameWithoutExtension = $pathInfo['filename'];
 		$extension = $pathInfo['extension'] ?? '';
 
 		$originalPath = $this->imageStorage->getPathToOriginalImage($filenameWithoutExtension, $extension);
-
-		$originalDir = dirname($originalPath);
-		if ( ! file_exists($originalDir)) {
-			mkdir($originalDir, permissions: 0755, recursive: true);
-		}
+		DirectoryUtils::ensureDirectoryExists(dirname($originalPath));
 
 		copy($file, $originalPath);
 		if (!$isCopy && $file !== ($originalPath)) {
 			unlink($file);
 		}
 
-		if ($this->configService->get('imageOriginal', 'resize')) {
-			// resize Orig Image - to save space on disk
-			$NewImageWidth      = $this->configService->get('imageOriginal', 'width'); //New Width of Image
-			$NewImageHeight     = $this->configService->get('imageOriginal', 'height'); // New Height of Image
-			$Quality        = 95; //Image Quality
-			$imagePath = $originalPath;
-			$destPath = $originalPath;
-			$checkValidImage = @getimagesize($imagePath);
-			if (file_exists($imagePath) && $checkValidImage !== false) {
-				//Continue only if 2 given parameters are true
-				//Image looks valid, resize.
-				$this->resizeImage($imagePath, $destPath, $NewImageWidth, $NewImageHeight, $Quality);
-			}
+		try{
+			[$width, $height] = ImageHelper::resolveImageSize($this->configService, $originalPath);
+			$newImage->width = $width;
+			$newImage->height = $height;
+		}catch (UnknownImageFileException|ImageException|LogicException $e) {
+			Debugger::log($e);
 		}
 
 		$newImage->filename = $filename;
@@ -85,42 +78,28 @@
 		return $newImage;
 	}
 
-	private function sanitize(string $filename): string
+	public function recalculateSizes(LibraryImage $libraryImage): array
 	{
-		$name = Strings::webalize($filename, '.', false);
-		$name = str_replace(['-.', '.-'], '.', $name);
-		$name = trim($name, '.-');
-		$name = mb_strtolower($name);
-
-		return $name === '' ? 'unknown' : $name;
-	}
+		$pathInfo = pathinfo($libraryImage->filename);
+		$originalPath = $this->imageStorage->getPathToOriginalImage($pathInfo['filename'], $pathInfo['extension'] ?? '');
 
-	// TODO REF presunout - mela by existovat jedna ovecna classa na resize obrazku
-	private function resizeImage(string $SrcImage, string $DestImage, int $MaxWidth, int $MaxHeight, int $Quality): bool
-	{
-		$imageSize = getimagesize($SrcImage);
-		\assert($imageSize !== false);
+		try{
+			[$width, $height] = ImageHelper::resolveImageSize($this->configService, $originalPath);
+			$libraryImage->width = $width;
+			$libraryImage->height = $height;
 
-		[$iWidth, $iHeight] = $imageSize;
+			$this->orm->libraryImage->persistAndFlush($libraryImage);
 
-		if ($iWidth <= $MaxWidth && $iHeight <= $MaxHeight) {
-			return false;
+			return [$width, $height];
+		}catch (UnknownImageFileException|ImageException|LogicException $e) {
+			Debugger::log($e);
+			return [$libraryImage->width, $libraryImage->height];
 		}
 
-		$ImageScale  = min($MaxWidth / $iWidth, $MaxHeight / $iHeight);
-		$NewWidth    = ceil($ImageScale * $iWidth);
-		$NewHeight   = ceil($ImageScale * $iHeight);
-
-		$image = Image::fromFile($SrcImage);
-		$NewWidth = (int) $NewWidth;
-		$NewHeight = (int) $NewHeight;
-		$image->resize($NewWidth, $NewHeight);
-		$image->save($DestImage);
-		return true;
 	}
 
-    public function deleteImage(LibraryImage $image): void
-    {
+	public function deleteImage(LibraryImage $image): void
+	{
 		$fileInfo = pathinfo($image->filename);
 
 		if (isset($fileInfo['extension'])) {
@@ -129,16 +108,15 @@
 		}
 
 		$this->orm->libraryImage->removeAndFlush($image);
-    }
+	}
 
-
 	public function getImageUsage(LibraryImage $image, Mutation $mutation): array
 	{
 		$esIndex = $this->orm->esIndex->getAllLastActive($mutation);
 		$usage = [];
 		if ($esIndex !== null) {
 			$usage = array_map(
-				function (\stdClass $data) {
+				static function (\stdClass $data) {
 					return $data->name;
 				},
 				$this->esAllRepository->searchImageUsage($esIndex, $image)
Index: app/Model/Utils/DirectoryUtils.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/Model/Utils/DirectoryUtils.php b/app/Model/Utils/DirectoryUtils.php
new file mode 100644
--- /dev/null	(revision fa437f3bd86c87c1bb86a472e9b50fd656888030)
+++ b/app/Model/Utils/DirectoryUtils.php	(revision fa437f3bd86c87c1bb86a472e9b50fd656888030)
@@ -0,0 +1,13 @@
+<?php declare(strict_types = 1);
+
+namespace App\Model\Utils;
+
+final class DirectoryUtils
+{
+	public static function ensureDirectoryExists(string $path): void
+	{
+		if (!file_exists($path) && !mkdir($path, 0755, true) && !is_dir($path)) {
+			throw new \RuntimeException(sprintf('Directory "%s" was not created', $path));
+		}
+	}
+}
Index: app/Model/Utils/FilenameUtils.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/Model/Utils/FilenameUtils.php b/app/Model/Utils/FilenameUtils.php
new file mode 100644
--- /dev/null	(revision fa437f3bd86c87c1bb86a472e9b50fd656888030)
+++ b/app/Model/Utils/FilenameUtils.php	(revision fa437f3bd86c87c1bb86a472e9b50fd656888030)
@@ -0,0 +1,18 @@
+<?php declare(strict_types = 1);
+
+namespace App\Model\Utils;
+
+use Nette\Utils\Strings;
+
+final class FilenameUtils
+{
+	public static function sanitize(string $filename): string
+	{
+		$name = Strings::webalize($filename, '.', false);
+		$name = str_replace(['-.', '.-'], '.', $name);
+		$name = trim($name, '.-');
+		$name = mb_strtolower($name);
+
+		return $name === '' ? 'unknown' : $name;
+	}
+}
Index: migrations/core/structures/2025-06-17-173900-add-width-height-to-image.sql
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/migrations/core/structures/2025-06-17-173900-add-width-height-to-image.sql b/migrations/core/structures/2025-06-17-173900-add-width-height-to-image.sql
new file mode 100644
--- /dev/null	(revision fa437f3bd86c87c1bb86a472e9b50fd656888030)
+++ b/migrations/core/structures/2025-06-17-173900-add-width-height-to-image.sql	(revision fa437f3bd86c87c1bb86a472e9b50fd656888030)
@@ -0,0 +1,3 @@
+ALTER TABLE `image`
+ADD `width` int(11) NOT NULL DEFAULT '0',
+ADD `height` int(11) NOT NULL DEFAULT '0' AFTER `width`;
